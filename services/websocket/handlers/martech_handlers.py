from typing import Union
import json
import os

from schemas.manual_schema import ManualPayloadMartech
from services.srm_service import process_smr
from usecases.handle_task_logic import send_task_martech
from utils.factory import convert_string_to_base64
from utils.logger import get_logger

logger = get_logger("martech_handlers")

def load_smart_responses():
    """Load smart responses từ file config"""
    try:
        config_path = "configs/smart_responses.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"File config không tồn tại: {config_path}")
            return []
    except Exception as e:
        logger.error(f"Lỗi khi đọc file config: {e}")
        return []

def find_response_by_query(query_text: str):
    """Tìm response dựa trên query text"""
    responses = load_smart_responses()
    for response in responses:
        if response.get("query", "").upper() in query_text.upper():
            return response
    return None

async def handle_martech_order(type_order: str, payload: Union[dict, ManualPayloadMartech]):
    try:
        # Chuẩn hóa payload về ManualPayloadMartech
        data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
        logger.info(f"Nhận đơn hàng {type_order}: {data}")

        if data.text.startswith('LYNKCO'):
            query = data.text
            logger.info(f"Phát hiện query: {query}")

            # Tìm response từ config
            response_data = find_response_by_query(query)
            if response_data:
                logger.info(f"Tìm thấy response cho query: {query}")
                # Sử dụng final_markdown nếu có, không thì dùng markdown
                final_text = response_data.get("final_markdown") or response_data.get("markdown", "")
                dataMartech = convert_string_to_base64(final_text)
            else:
                logger.warning(f"Không tìm thấy response cho query: {query}")
                dataMartech = convert_string_to_base64(data.text)
        else:
            # Mặc định: lấy text gốc -> base64
            dataMartech = convert_string_to_base64(data.text or "")

            # Nếu cần gọi SMR để tạo markdown cuối cùng
            if getattr(data, "isCallSmr", False):
                smr_result = process_smr(data.text or "")
                if isinstance(smr_result, dict):
                    final_markdown = smr_result.get("final_markdown") or ""
                    dataMartech = convert_string_to_base64(final_markdown)
                else:
                    logger.warning("process_smr không trả về dict, dùng text gốc thay thế.")
        await send_task_martech(dataMartech, data.name_app)

    except Exception as e:
        logger.exception(f"❌ Lỗi khi xử lý martech_order: {e}")


