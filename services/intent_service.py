import httpx
from core.config import settings

async def process_intent(text: str, valid_location: list = None):
    payload = {
        "text": text,
        "valid_location": valid_location or []
    }
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(f"{settings.api.intent}parse", json=payload, timeout=5)
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}

async def call_llm_stream(query: str):
    """
    Gọi LLM service để xử lý query khi intent rỗng
    """
    payload = {
        "query": query
    }
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(f"{settings.api.srm}/parse", json=payload, timeout=30)
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}





import httpx
import json
from core.config import settings
from services.websocket.websocket_service import send_to

async def process_intent(text: str, valid_location: list = None):
    payload = {
        "text": text,
        "valid_location": valid_location or []
    }
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(f"{settings.api.intent}parse", json=payload, timeout=5)
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}


async def call_llm_stream(query: str, websocket=None):
    """
    Gọi LLM service để xử lý query với streaming response
    """
    payload = {"query": query}
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            url = f"{settings.api.srm}parse"
            headers = {
                "accept": "text/event-stream",
                "Content-Type": "application/json",
            }
            
            async with client.stream("POST", url, json=payload, headers=headers) as response:
                response.raise_for_status()
                print(f"Streaming response status: {response.status_code}")
                
                results = []
                buffer = ""
                
                async for chunk in response.aiter_text():
                    if chunk:
                        buffer += chunk
                        
                        # Xử lý từng dòng hoàn chỉnh
                        while "\n" in buffer:
                            line, buffer = buffer.split("\n", 1)
                            line = line.strip()
                            
                            if line:
                                print(f"Received line: {repr(line)}")
                                
                                # Xử lý SSE format
                                if line.startswith("data: "):
                                    data_content = line[6:].strip()
                                    if data_content and data_content != "[DONE]":
                                        try:
                                            json_data = json.loads(data_content)
                                            results.append(json_data)
                                            
                                            # Gửi qua websocket nếu có
                                            if websocket:
                                                await websocket.send_text(json.dumps(json_data))
                                        except json.JSONDecodeError:
                                            results.append({"content": data_content})
                                            if websocket:
                                                await websocket.send_text(data_content)
                                elif line.startswith("{"):
                                    # JSON trực tiếp
                                    try:
                                        json_data = json.loads(line)
                                        results.append(json_data)
                                        if websocket:
                                            await websocket.send_text(json.dumps(json_data))
                                    except json.JSONDecodeError:
                                        results.append({"content": line})
                                        if websocket:
                                            await websocket.send_text(line)
                
                # Xử lý buffer còn lại
                if buffer.strip():
                    await send_to("martech_app", {
                        "type": "martech_order",
                        "payload": task.dict()
                    })
                    # results.append({"content": buffer.strip()})
                    # if websocket:
                    #     await websocket.send_text(buffer.strip())
                
                return {"results": results, "status": "completed"}
                
    except httpx.HTTPStatusError as e:
        error_msg = f"HTTP error {e.response.status_code}"
        return {"error": error_msg}
    except httpx.TimeoutException:
        return {"error": "Request timeout"}
    except Exception as e:
        return {"error": str(e)}

