from pydantic import BaseModel, Field
from typing import Optional, List


class Move(BaseModel):
    Name: Optional[str] = None
    X: Optional[float] = None
    Y: Optional[float] = None
    YAW: Optional[float] = None


class Sound(BaseModel):
    uri: Optional[str] = None
    RepeatPerSec: Optional[int] = None
    RepeatNum: Optional[int] = None
    DepayPerSec: Optional[int] = None


class Media(BaseModel):
    uri: Optional[str] = None
    RepeatPerSec: Optional[int] = None
    RepeatNum: Optional[int] = None
    DepayPerSec: Optional[int] = None


class MarTech(BaseModel):
    text: Optional[str] = None


class Other(BaseModel):
    uri: Optional[str] = None
    RepeatPerSec: Optional[int] = None
    RepeatNum: Optional[int] = None
    DepayPerSec: Optional[int] = None


class Stage(BaseModel):
    move: Optional[Move] = Field(default=None, alias="Move")
    sound: Optional[Sound] = Field(default=None, alias="Sound")
    media: Optional[Media] = Field(default=None, alias="Media")
    martech: Optional[MarTech] = Field(default=None, alias="MarTech")
    other: Optional[Other] = Field(default=None, alias="Other")

    model_config = {
        "populate_by_name": True,
        "extra": "ignore"
    }


class Action(BaseModel):
    Id: Optional[str] = None
    NextActionId: Optional[str] = None
    Begin: Optional[Stage] = None
    Inprogess: Optional[Stage] = None
    Done: Optional[Stage] = None


class TaskItem(BaseModel):
    action: Optional[Action] = Field(default=None, alias="Action")


class TaskSeqManage(BaseModel):
    Id: Optional[str] = None
    Name: Optional[str] = None
    Type: Optional[str] = None
    Status: Optional[str] = None
    Tasks: Optional[List[TaskItem]] = None
    CreatedTime: Optional[str] = None
