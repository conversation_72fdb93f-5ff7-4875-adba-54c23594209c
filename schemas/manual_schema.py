from pydantic import BaseModel

# Payload chung
class ManualPayload(BaseModel):
    text: str
    name_app: str

# <PERSON><PERSON><PERSON> yêu cầu tổng thể
class ManualRequest(BaseModel):
    type: str
    payload: ManualPayload

# Trường hợp không thêm gì mới
class ManualRequestSound(ManualRequest):
    pass

# Thêm isCallSmr trong payload
class ManualPayloadMartech(ManualPayload):
    isCallSmr: bool = None

class ManualRequestMartech(BaseModel):
    type: str
    payload: ManualPayloadMartech
