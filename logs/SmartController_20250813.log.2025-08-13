2025-08-13 16:32:53.875608 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ <PERSON><PERSON><PERSON> hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-13T09:32:53.874449Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 16:32:53.877061 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-13T09:32:53.876357Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 16:32:53.877602 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-13T09:32:53.877540Z', 'level': 'info'}
2025-08-13 16:32:53.886683 [DEB] asyncio	Using selector: EpollSelector
2025-08-13 16:32:54.693670 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại **********:8000 với hostname 'controller.local'", 'timestamp': '2025-08-13T09:32:54.693512Z', 'level': 'info'}
2025-08-13 16:32:57.917548 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T09:32:57.917430Z', 'level': 'info'}
2025-08-13 16:32:57.921769 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T09:32:57.921683Z', 'level': 'info'}
2025-08-13 16:38:27.362141 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-13T09:38:27.361990Z', 'level': 'debug'}
2025-08-13 16:38:27.367982 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-13T09:38:27.365804Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-13 16:38:30.246207 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-13T09:38:30.246060Z', 'level': 'debug'}
2025-08-13 16:38:30.247560 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-13T09:38:30.246985Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-13 16:38:37.761532 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-13T09:38:37.761410Z', 'level': 'debug'}
2025-08-13 16:38:37.762514 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-13T09:38:37.762133Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-13 16:38:39.155376 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_01"}}', 'timestamp': '2025-08-13T09:38:39.155222Z', 'level': 'debug'}
2025-08-13 16:38:39.156699 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_01'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-13T09:38:39.156143Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_01\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_01'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-13 16:38:39.823946 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-13T09:38:39.823814Z', 'level': 'debug'}
2025-08-13 16:38:39.824950 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-13T09:38:39.824558Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-13 17:35:43.229890 [INF] websocket	{'event': 'WebSocket ngắt kết nối: device_app', 'timestamp': '2025-08-13T10:35:43.229763Z', 'level': 'info'}
2025-08-13 17:35:43.230298 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T10:35:43.230251Z', 'level': 'info'}
2025-08-13 17:35:43.230775 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T10:35:43.230732Z', 'level': 'info'}
2025-08-13 17:36:28.740764 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-13T10:36:28.738998Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 17:36:28.743215 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-13T10:36:28.742252Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 17:36:28.744020 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-13T10:36:28.743923Z', 'level': 'info'}
2025-08-13 17:36:28.774219 [DEB] asyncio	Using selector: EpollSelector
2025-08-13 17:36:29.582968 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại **********:8000 với hostname 'controller.local'", 'timestamp': '2025-08-13T10:36:29.582829Z', 'level': 'info'}
2025-08-13 17:36:31.192084 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T10:36:31.191977Z', 'level': 'info'}
2025-08-13 17:36:31.199160 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T10:36:31.199049Z', 'level': 'info'}
2025-08-13 17:47:50.261232 [INF] websocket	{'event': 'WebSocket ngắt kết nối: device_app', 'timestamp': '2025-08-13T10:47:50.261110Z', 'level': 'info'}
2025-08-13 17:47:50.261723 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T10:47:50.261669Z', 'level': 'info'}
2025-08-13 17:47:50.262284 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T10:47:50.262235Z', 'level': 'info'}
2025-08-13 17:48:36.303277 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-13T10:48:36.301835Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 17:48:36.306317 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-13T10:48:36.305104Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 17:48:36.307723 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-13T10:48:36.306943Z', 'level': 'info'}
2025-08-13 17:48:36.319080 [DEB] asyncio	Using selector: EpollSelector
2025-08-13 17:48:37.128838 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại **********:8000 với hostname 'controller.local'", 'timestamp': '2025-08-13T10:48:37.128688Z', 'level': 'info'}
2025-08-13 17:48:38.349383 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T10:48:38.349273Z', 'level': 'info'}
2025-08-13 17:48:38.359429 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T10:48:38.359315Z', 'level': 'info'}
2025-08-13 17:58:57.514812 [INF] sound_handlers	{'event': "Nhận đơn hàng sound_order: {'text': 'chào mọi người nhé lại co đăm rồi hu hu', 'name_app': 'device_app'}", 'timestamp': '2025-08-13T10:58:57.514650Z', 'level': 'info'}
2025-08-13 17:58:57.645709 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8003 local_address=None timeout=5 socket_options=None
2025-08-13 17:58:57.648125 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff99cc0a00>
2025-08-13 17:58:57.648745 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'POST']>
2025-08-13 17:58:57.649571 [DEB] httpcore.http11	send_request_headers.complete
2025-08-13 17:58:57.649796 [DEB] httpcore.http11	send_request_body.started request=<Request [b'POST']>
2025-08-13 17:58:57.650548 [DEB] httpcore.http11	send_request_body.complete
2025-08-13 17:58:57.651013 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'POST']>
2025-08-13 17:58:58.862358 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Wed, 13 Aug 2025 10:58:57 GMT'), (b'server', b'uvicorn'), (b'content-length', b'85'), (b'content-type', b'application/json')])
2025-08-13 17:58:58.864637 [INF] httpx	HTTP Request: POST http://0.0.0.0:8003/tts "HTTP/1.1 200 OK"
2025-08-13 17:58:58.865276 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'POST']>
2025-08-13 17:58:58.865660 [DEB] httpcore.http11	receive_response_body.complete
2025-08-13 17:58:58.866007 [DEB] httpcore.http11	response_closed.started
2025-08-13 17:58:58.866361 [DEB] httpcore.http11	response_closed.complete
2025-08-13 17:58:58.866969 [DEB] httpcore.connection	close.started
2025-08-13 17:58:58.867392 [DEB] httpcore.connection	close.complete
2025-08-13 17:58:58.868666 [INF] websocket	{'event': "Gửi đến device_app: {'type': 'task_order', 'payload': {'Id': '26358135902285572564', 'Name': 'Manual_Test', 'Type': 'Manual_Test', 'Status': 'Queue', 'Tasks': [{'action': {'Id': 'Manual_Test', 'NextActionId': None, 'Begin': None, 'Inprogess': {'move': None, 'sound': {'uri': 'http://0.0.0.0:8003/download/d09baa45-abf5-4ac2-85e2-2fd6bac54c6b', 'RepeatPerSec': 0, 'RepeatNum': 0, 'DepayPerSec': 0}, 'media': None, 'martech': None, 'other': None}, 'Done': None}}], 'CreatedTime': '2025-08-13T10:58:58Z'}}", 'timestamp': '2025-08-13T10:58:58.868589Z', 'level': 'info'}
2025-08-13 18:08:08.098185 [INF] websocket	{'event': 'WebSocket ngắt kết nối: device_app', 'timestamp': '2025-08-13T11:08:08.098077Z', 'level': 'info'}
2025-08-13 18:08:08.098622 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T11:08:08.098572Z', 'level': 'info'}
2025-08-13 18:08:08.099125 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T11:08:08.099084Z', 'level': 'info'}
2025-08-13 18:08:54.318758 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-13T11:08:54.317382Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 18:08:54.322681 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-13T11:08:54.321865Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-13 18:08:54.323352 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-13T11:08:54.323278Z', 'level': 'info'}
2025-08-13 18:08:54.329907 [DEB] asyncio	Using selector: EpollSelector
2025-08-13 18:08:55.142205 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại **********:8000 với hostname 'controller.local'", 'timestamp': '2025-08-13T11:08:55.142069Z', 'level': 'info'}
2025-08-13 18:08:55.916662 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T11:08:55.916516Z', 'level': 'info'}
2025-08-13 18:08:55.935881 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-13T11:08:55.935734Z', 'level': 'info'}
2025-08-13 18:34:32.821109 [INF] websocket	{'event': 'WebSocket ngắt kết nối: device_app', 'timestamp': '2025-08-13T11:34:32.820971Z', 'level': 'info'}
2025-08-13 18:34:32.821773 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T11:34:32.821700Z', 'level': 'info'}
2025-08-13 18:34:32.822422 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-13T11:34:32.822351Z', 'level': 'info'}
