2025-08-14 13:10:26.550351 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ <PERSON><PERSON><PERSON> hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-14T06:10:26.548361Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 13:10:26.552051 [DEB] asyncio	Using selector: EpollSelector
2025-08-14 13:10:26.552383 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-14T06:10:26.551393Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 13:10:26.553426 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-14T06:10:26.553350Z', 'level': 'info'}
2025-08-14 13:10:27.303868 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-14T06:10:27.303559Z', 'level': 'info'}
2025-08-14 13:10:27.363893 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại 127.0.0.1:8000 với hostname 'controller.local'", 'timestamp': '2025-08-14T06:10:27.363793Z', 'level': 'info'}
2025-08-14 13:39:21.116322 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:39:21.116186Z', 'level': 'debug'}
2025-08-14 13:39:21.122288 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:39:21.120119Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:39.457779 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:40:39.457665Z', 'level': 'debug'}
2025-08-14 13:40:39.458692 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:39.458340Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:40.378075 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:40:40.377963Z', 'level': 'debug'}
2025-08-14 13:40:40.379249 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:40.378745Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:45.905946 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-14T06:40:45.905873Z', 'level': 'debug'}
2025-08-14 13:40:45.906840 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:45.906372Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:47.135921 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_01"}}', 'timestamp': '2025-08-14T06:40:47.135814Z', 'level': 'debug'}
2025-08-14 13:40:47.137017 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_01'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:47.136529Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_01\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_01'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:47.698785 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:40:47.698727Z', 'level': 'debug'}
2025-08-14 13:40:47.699334 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:47.699102Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:49.615541 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:40:49.615480Z', 'level': 'debug'}
2025-08-14 13:40:49.616135 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:49.615887Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:40:50.207643 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:40:50.207588Z', 'level': 'debug'}
2025-08-14 13:40:50.208191 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:40:50.207961Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:31.694165 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:44:31.694066Z', 'level': 'debug'}
2025-08-14 13:44:31.695022 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:31.694686Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:32.926209 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:44:32.926091Z', 'level': 'debug'}
2025-08-14 13:44:32.927414 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:32.926904Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:33.543187 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:44:33.543121Z', 'level': 'debug'}
2025-08-14 13:44:33.543797 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:33.543543Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:42.447980 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:44:42.447895Z', 'level': 'debug'}
2025-08-14 13:44:42.448719 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:42.448420Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:43.060520 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-14T06:44:43.060458Z', 'level': 'debug'}
2025-08-14 13:44:43.061505 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:43.061028Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:50.437837 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:44:50.437762Z', 'level': 'debug'}
2025-08-14 13:44:50.438558 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:50.438264Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:53.202768 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:44:53.202688Z', 'level': 'debug'}
2025-08-14 13:44:53.203500 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:53.203207Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:44:54.122638 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:44:54.122576Z', 'level': 'debug'}
2025-08-14 13:44:54.123228 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:44:54.122981Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:54.065713 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:47:54.065617Z', 'level': 'debug'}
2025-08-14 13:47:54.066588 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:54.066228Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:54.754213 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-14T06:47:54.754143Z', 'level': 'debug'}
2025-08-14 13:47:54.754886 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:54.754612Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:55.677830 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:47:55.677764Z', 'level': 'debug'}
2025-08-14 13:47:55.678502 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:55.678218Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:55.877456 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:47:55.877406Z', 'level': 'debug'}
2025-08-14 13:47:55.877956 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:55.877736Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:56.290488 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:47:56.290430Z', 'level': 'debug'}
2025-08-14 13:47:56.291057 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:56.290829Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:56.907405 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:47:56.907349Z', 'level': 'debug'}
2025-08-14 13:47:56.907927 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:56.907707Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:58.233918 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:47:58.233854Z', 'level': 'debug'}
2025-08-14 13:47:58.234509 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:58.234253Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:59.361655 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:47:59.361547Z', 'level': 'debug'}
2025-08-14 13:47:59.362739 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:59.362237Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:47:59.670081 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:47:59.670027Z', 'level': 'debug'}
2025-08-14 13:47:59.670592 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:47:59.670375Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:00.286138 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:48:00.286066Z', 'level': 'debug'}
2025-08-14 13:48:00.286822 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:00.286544Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:01.209093 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-14T06:48:01.209035Z', 'level': 'debug'}
2025-08-14 13:48:01.209627 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:01.209399Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:05.810099 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:48:05.809988Z', 'level': 'debug'}
2025-08-14 13:48:05.811232 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:05.810741Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:06.434876 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:48:06.434818Z', 'level': 'debug'}
2025-08-14 13:48:06.435396 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:06.435179Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:08.376569 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_09"}}', 'timestamp': '2025-08-14T06:48:08.376509Z', 'level': 'debug'}
2025-08-14 13:48:08.377102 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:08.376876Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_09\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_09'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:09.299567 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_06"}}', 'timestamp': '2025-08-14T06:48:09.299501Z', 'level': 'debug'}
2025-08-14 13:48:09.300128 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:09.299892Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_06\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_06'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:09.808563 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_05"}}', 'timestamp': '2025-08-14T06:48:09.808509Z', 'level': 'debug'}
2025-08-14 13:48:09.809079 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:09.808852Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_05\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_05'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:11.342077 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-14T06:48:11.341997Z', 'level': 'debug'}
2025-08-14 13:48:11.342788 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:11.342503Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:11.957575 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_01"}}', 'timestamp': '2025-08-14T06:48:11.957517Z', 'level': 'debug'}
2025-08-14 13:48:11.958112 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_01'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:11.957879Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_01\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_01'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:48:12.877853 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"martech_order","payload":{"text":"{LYNKCO_03"}}', 'timestamp': '2025-08-14T06:48:12.877748Z', 'level': 'debug'}
2025-08-14 13:48:12.878927 [ERR] martech_handlers	{'event': "❌ Lỗi khi xử lý martech_order: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", 'timestamp': '2025-08-14T06:48:12.878460Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order\n    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)\n  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__\n    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)\npydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech\nname_app\n  Field required [type=missing, input_value={\'text\': \'{LYNKCO_03\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/websocket/handlers/martech_handlers.py", line 14, in handle_martech_order
    data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
  File "/home/<USER>/MainRobot/SmartController/venv/lib/python3.8/site-packages/pydantic/main.py", line 214, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for ManualPayloadMartech
name_app
  Field required [type=missing, input_value={'text': '{LYNKCO_03'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-08-14 13:52:25.848291 [INF] websocket	{'event': 'WebSocket ngắt kết nối: device_app', 'timestamp': '2025-08-14T06:52:25.848152Z', 'level': 'info'}
2025-08-14 13:52:25.848948 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '', 'name': '', 'status': False}", 'timestamp': '2025-08-14T06:52:25.848879Z', 'level': 'info'}
2025-08-14 15:47:17.184298 [INF] handle_asr_logic	{'event': 'Nhận lệnh: 啊 anh anh ở cái mảng thế giới anh sẽ cho nó trời để cho người ta hiểu là nó đang nghĩ sau khi mà lộn xong rồi thì view mà đang', 'timestamp': '2025-08-14T08:47:17.184181Z', 'level': 'info'}
2025-08-14 15:47:17.305158 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:17.307620 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9e9c5280>
2025-08-14 15:47:17.308109 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:17.308681 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:17.308834 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:17.309080 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:17.309212 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:17.314519 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:16 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:17.315667 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:17.316020 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:17.316669 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:17.316932 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:17.317177 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:17.317733 [DEB] httpcore.connection	close.started
2025-08-14 15:47:17.318088 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:17.328813 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:17.330445 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:17.332011 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:17.331922Z', 'level': 'debug'}
2025-08-14 15:47:17.332242 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:17.332209Z', 'level': 'debug'}
2025-08-14 15:47:25.191000 [INF] handle_asr_logic	{'event': 'Nhận lệnh: bán như thế ở dưới kia bán theo kiểu con sôn trong nốt sau này âu cái màn hình bán dao ra', 'timestamp': '2025-08-14T08:47:25.190901Z', 'level': 'info'}
2025-08-14 15:47:25.200306 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:25.201870 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d843d60>
2025-08-14 15:47:25.202299 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:25.202843 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:25.202996 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:25.203342 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:25.203481 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:25.205192 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:24 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:25.205718 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:25.205992 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:25.206504 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:25.206729 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:25.206941 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:25.207382 [DEB] httpcore.connection	close.started
2025-08-14 15:47:25.207674 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:25.216707 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:25.217952 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:25.219594 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:25.219517Z', 'level': 'debug'}
2025-08-14 15:47:25.219858 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:25.219816Z', 'level': 'debug'}
2025-08-14 15:47:31.747094 [INF] handle_asr_logic	{'event': 'Nhận lệnh: đấy không được như thế ít nhất là để để cho người ta biết là con robot này nó đang sinh viên', 'timestamp': '2025-08-14T08:47:31.746994Z', 'level': 'info'}
2025-08-14 15:47:31.756338 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:31.757812 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d8161f0>
2025-08-14 15:47:31.758230 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:31.758768 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:31.758922 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:31.759277 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:31.759415 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:31.761072 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:31 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:31.761582 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:31.761854 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:31.762377 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:31.762603 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:31.762813 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:31.763255 [DEB] httpcore.connection	close.started
2025-08-14 15:47:31.763552 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:31.772573 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:31.773862 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:31.775403 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:31.775320Z', 'level': 'debug'}
2025-08-14 15:47:31.775661 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:31.775623Z', 'level': 'debug'}
2025-08-14 15:47:33.250787 [INF] handle_asr_logic	{'event': 'Nhận lệnh: chưa', 'timestamp': '2025-08-14T08:47:33.250690Z', 'level': 'info'}
2025-08-14 15:47:33.259923 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:33.261349 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d81a760>
2025-08-14 15:47:33.261765 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:33.262249 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:33.262421 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:33.262651 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:33.262782 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:33.264572 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:32 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:33.265093 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:33.265353 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:33.265839 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:33.266052 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:33.266258 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:33.266700 [DEB] httpcore.connection	close.started
2025-08-14 15:47:33.266987 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:33.275834 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:33.276942 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:33.283329 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:33.283242Z', 'level': 'debug'}
2025-08-14 15:47:33.283634 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:33.283592Z', 'level': 'debug'}
2025-08-14 15:47:34.792312 [INF] handle_asr_logic	{'event': 'Nhận lệnh: nhưng mà', 'timestamp': '2025-08-14T08:47:34.792217Z', 'level': 'info'}
2025-08-14 15:47:34.801450 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:34.802976 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d8bb0d0>
2025-08-14 15:47:34.803401 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:34.803901 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:34.804046 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:34.804265 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:34.804391 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:34.806192 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:34 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:34.806741 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:34.807009 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:34.807492 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:34.807709 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:34.807917 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:34.808346 [DEB] httpcore.connection	close.started
2025-08-14 15:47:34.808640 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:34.817256 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:34.818311 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:34.819676 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:34.819612Z', 'level': 'debug'}
2025-08-14 15:47:34.819908 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:34.819876Z', 'level': 'debug'}
2025-08-14 15:47:37.520238 [INF] handle_asr_logic	{'event': 'Nhận lệnh: cái ơ', 'timestamp': '2025-08-14T08:47:37.520142Z', 'level': 'info'}
2025-08-14 15:47:37.529448 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:37.530935 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d983130>
2025-08-14 15:47:37.531364 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:37.531867 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:37.532024 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:37.532367 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:37.532509 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:37.534244 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:36 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:37.535092 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:37.535377 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:37.535895 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:37.536117 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:37.536335 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:37.536783 [DEB] httpcore.connection	close.started
2025-08-14 15:47:37.537079 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:37.545887 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:37.547115 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:37.548685 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:37.548611Z', 'level': 'debug'}
2025-08-14 15:47:37.548949 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:37.548913Z', 'level': 'debug'}
2025-08-14 15:47:44.767777 [INF] handle_asr_logic	{'event': 'Nhận lệnh: con kia của mày bật lên ý nó phải được bởi vì thành phần ulin nó làm được', 'timestamp': '2025-08-14T08:47:44.767681Z', 'level': 'info'}
2025-08-14 15:47:44.776952 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:44.778486 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d8228e0>
2025-08-14 15:47:44.778904 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:44.779407 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:44.779556 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:44.779782 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:44.779911 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:44.781660 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:44 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:44.782162 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:44.782451 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:44.782946 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:44.783166 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:44.783381 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:44.783815 [DEB] httpcore.connection	close.started
2025-08-14 15:47:44.784103 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:44.792736 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:44.793775 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:44.795238 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:44.795172Z', 'level': 'debug'}
2025-08-14 15:47:44.795478 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:44.795440Z', 'level': 'debug'}
2025-08-14 15:47:49.020883 [INF] handle_asr_logic	{'event': 'Nhận lệnh: trên mạng nó làm được thì cái hạ tầng này hay tùng nguyên tắc', 'timestamp': '2025-08-14T08:47:49.020790Z', 'level': 'info'}
2025-08-14 15:47:49.030018 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:49.031529 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d823340>
2025-08-14 15:47:49.032001 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:49.032512 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:49.032665 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:49.032897 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:49.033027 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:49.034865 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:48 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:49.035594 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:49.035885 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:49.036518 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:49.036744 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:49.036959 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:49.037465 [DEB] httpcore.connection	close.started
2025-08-14 15:47:49.037766 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:49.051376 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:49.054667 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:49.056380 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:49.056310Z', 'level': 'debug'}
2025-08-14 15:47:49.056612 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:49.056572Z', 'level': 'debug'}
2025-08-14 15:47:51.406482 [INF] handle_asr_logic	{'event': 'Nhận lệnh: em em không tìm vào tay', 'timestamp': '2025-08-14T08:47:51.406383Z', 'level': 'info'}
2025-08-14 15:47:51.415687 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:51.417137 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d8165b0>
2025-08-14 15:47:51.417553 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:51.418044 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:51.418189 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:51.418417 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:51.418543 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:51.420407 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:50 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:51.420934 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:51.421198 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:51.421681 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:51.421894 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:51.422104 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:51.422561 [DEB] httpcore.connection	close.started
2025-08-14 15:47:51.422853 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:51.431676 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:51.432797 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:51.434521 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:51.434454Z', 'level': 'debug'}
2025-08-14 15:47:51.434791 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:51.434754Z', 'level': 'debug'}
2025-08-14 15:47:57.460869 [INF] handle_asr_logic	{'event': 'Nhận lệnh: mười sáu ghi cơ mà anh biết ngay từ đây anh em vào', 'timestamp': '2025-08-14T08:47:57.460768Z', 'level': 'info'}
2025-08-14 15:47:57.470109 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:57.471702 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d822b20>
2025-08-14 15:47:57.472133 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:47:57.472685 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:47:57.472867 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:47:57.473099 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:47:57.473234 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:47:57.475098 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:47:56 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:47:57.475651 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:47:57.475948 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:47:57.476483 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:47:57.476724 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:47:57.476958 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:47:57.477423 [DEB] httpcore.connection	close.started
2025-08-14 15:47:57.477738 [DEB] httpcore.connection	close.complete
2025-08-14 15:47:57.487618 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:47:57.488793 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:47:57.490497 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:47:57.490422Z', 'level': 'debug'}
2025-08-14 15:47:57.490785 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:47:57.490743Z', 'level': 'debug'}
2025-08-14 15:48:04.772821 [INF] handle_asr_logic	{'event': 'Nhận lệnh: rồi để anh chạy lại xong anh lấp cho', 'timestamp': '2025-08-14T08:48:04.772725Z', 'level': 'info'}
2025-08-14 15:48:04.782015 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 15:48:04.783513 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9d938160>
2025-08-14 15:48:04.783939 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 15:48:04.784444 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 15:48:04.784600 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 15:48:04.784952 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 15:48:04.785091 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 15:48:04.786766 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 08:48:04 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 15:48:04.787294 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 15:48:04.787591 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 15:48:04.788092 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 15:48:04.788314 [DEB] httpcore.http11	response_closed.started
2025-08-14 15:48:04.788525 [DEB] httpcore.http11	response_closed.complete
2025-08-14 15:48:04.788957 [DEB] httpcore.connection	close.started
2025-08-14 15:48:04.789243 [DEB] httpcore.connection	close.complete
2025-08-14 15:48:04.797962 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 15:48:04.799227 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 15:48:04.800868 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T08:48:04.800795Z', 'level': 'debug'}
2025-08-14 15:48:04.801148 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T08:48:04.801104Z', 'level': 'debug'}
2025-08-14 15:51:26.764654 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-14T08:51:26.763937Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 15:51:26.767421 [DEB] asyncio	Using selector: EpollSelector
2025-08-14 15:51:26.767800 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-14T08:51:26.766513Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 15:51:26.768484 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-14T08:51:26.768437Z', 'level': 'info'}
2025-08-14 15:52:12.167922 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-14T08:52:12.167232Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 15:52:12.170466 [DEB] asyncio	Using selector: EpollSelector
2025-08-14 15:52:12.171162 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-14T08:52:12.170259Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 15:52:12.172382 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-14T08:52:12.172336Z', 'level': 'info'}
2025-08-14 15:52:43.706663 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-14T08:52:43.705920Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 15:52:43.709336 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-14T08:52:43.708461Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 15:52:43.709535 [DEB] asyncio	Using selector: EpollSelector
2025-08-14 15:52:43.710073 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-14T08:52:43.710018Z', 'level': 'info'}
2025-08-14 15:52:44.520808 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại **********:8000 với hostname 'controller.local'", 'timestamp': '2025-08-14T08:52:44.520476Z', 'level': 'info'}
2025-08-14 15:52:44.639956 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '2025-08-14T08:52:44.639821Z', 'level': 'info'}
2025-08-14 15:52:44.761597 [DEB] websocket	{'event': 'Nhận từ device_app: {"type":"ping","payload":{"name":"device_app","ip":"***********"}}', 'timestamp': '2025-08-14T08:52:44.761445Z', 'level': 'debug'}
2025-08-14 15:52:44.762375 [INF] socket_commands	{'event': "Cập nhật ping từ device_app: {'ip': '***********', 'name': 'device_app', 'status': True}", 'timestamp': '2025-08-14T08:52:44.762256Z', 'level': 'info'}
2025-08-14 15:52:44.763652 [INF] websocket	{'event': "Gửi đến device_app: {'type': 'pong', 'payload': [{'ip': '***********', 'name': 'device_app', 'status': True}, {'ip': '', 'name': 'martech_app', 'status': False}, {'ip': '', 'name': 'device_slam', 'status': False}, {'ip': '', 'name': 'vision_app', 'status': False}, {'ip': '', 'name': 'ai_intent', 'status': False}, {'ip': '', 'name': 'ai_srm', 'status': False}]}", 'timestamp': '2025-08-14T08:52:44.763575Z', 'level': 'info'}
2025-08-14 16:00:35.971173 [INF] websocket	{'event': 'WebSocket ngắt kết nối: device_app', 'timestamp': '2025-08-14T09:00:35.970995Z', 'level': 'info'}
2025-08-14 16:00:35.971995 [INF] websocket	{'event': "Cập nhật trạng thái device_app => OFFLINE: {'ip': '***********', 'name': 'device_app', 'status': False}", 'timestamp': '2025-08-14T09:00:35.971924Z', 'level': 'info'}
2025-08-14 16:35:33.015244 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Cấu hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '2025-08-14T09:35:33.013025Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 16:35:33.016906 [DEB] asyncio	Using selector: EpollSelector
2025-08-14 16:35:33.017796 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '2025-08-14T09:35:33.016707Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
2025-08-14 16:35:33.019018 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '2025-08-14T09:35:33.018939Z', 'level': 'info'}
2025-08-14 16:35:33.827562 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại 127.0.0.1:8000 với hostname 'controller.local'", 'timestamp': '2025-08-14T09:35:33.827465Z', 'level': 'info'}
2025-08-14 18:57:10.384483 [INF] handle_asr_logic	{'event': 'Nhận lệnh: UAN DIA', 'timestamp': '2025-08-14T11:57:10.384028Z', 'level': 'info'}
2025-08-14 18:57:10.569862 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:10.572234 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff92809d30>
2025-08-14 18:57:10.572724 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:10.573313 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:10.573480 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:10.573742 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:10.573868 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:10.595925 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:10 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:10.597170 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:10.597507 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:10.598071 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:10.598313 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:10.598531 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:10.599031 [DEB] httpcore.connection	close.started
2025-08-14 18:57:10.599347 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:10.608873 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:10.610141 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:10.611787 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:10.611695Z', 'level': 'debug'}
2025-08-14 18:57:10.612074 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:10.612033Z', 'level': 'debug'}
2025-08-14 18:57:16.853737 [INF] handle_asr_logic	{'event': 'Nhận lệnh: vâng xin mời out', 'timestamp': '2025-08-14T11:57:16.853641Z', 'level': 'info'}
2025-08-14 18:57:16.862920 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:16.864371 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927e27c0>
2025-08-14 18:57:16.864753 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:16.865215 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:16.865344 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:16.865555 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:16.865670 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:16.867458 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:16 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:16.867921 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:16.868164 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:16.868598 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:16.868805 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:16.869000 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:16.869407 [DEB] httpcore.connection	close.started
2025-08-14 18:57:16.869718 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:16.879644 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:16.880871 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:16.882518 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:16.882441Z', 'level': 'debug'}
2025-08-14 18:57:16.882798 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:16.882759Z', 'level': 'debug'}
2025-08-14 18:57:22.628288 [INF] handle_asr_logic	{'event': 'Nhận lệnh: anh cũng buồn đấy ồi anh thích về lúc nào thì anh về bao giờ anh ở', 'timestamp': '2025-08-14T11:57:22.628192Z', 'level': 'info'}
2025-08-14 18:57:22.637508 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:22.638960 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ead60>
2025-08-14 18:57:22.639333 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:22.639793 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:22.639916 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:22.640110 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:22.640223 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:22.642053 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:22 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:22.642533 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:22.642787 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:22.643279 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:22.643491 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:22.643696 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:22.644125 [DEB] httpcore.connection	close.started
2025-08-14 18:57:22.644407 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:22.654945 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:22.659983 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:22.663433 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:22.663361Z', 'level': 'debug'}
2025-08-14 18:57:22.663671 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:22.663635Z', 'level': 'debug'}
2025-08-14 18:57:27.986398 [INF] handle_asr_logic	{'event': 'Nhận lệnh: xưa giờ luôn thế thế mày nghĩ tao làm ở đâu tao bảo ta', 'timestamp': '2025-08-14T11:57:27.986296Z', 'level': 'info'}
2025-08-14 18:57:27.995817 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:27.997485 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff92810af0>
2025-08-14 18:57:27.997952 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:27.998492 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:27.998638 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:27.998858 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:27.998979 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:28.000760 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:27 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:28.001316 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:28.001621 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:28.002141 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:28.002367 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:28.002583 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:28.003052 [DEB] httpcore.connection	close.started
2025-08-14 18:57:28.003368 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:28.012554 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:28.013862 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:28.015579 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:28.015503Z', 'level': 'debug'}
2025-08-14 18:57:28.015860 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:28.015822Z', 'level': 'debug'}
2025-08-14 18:57:33.388974 [INF] handle_asr_logic	{'event': 'Nhận lệnh: này có con còn nhớ không', 'timestamp': '2025-08-14T11:57:33.388878Z', 'level': 'info'}
2025-08-14 18:57:33.398161 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:33.399578 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ef130>
2025-08-14 18:57:33.399953 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:33.400414 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:33.400570 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:33.400776 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:33.400892 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:33.402644 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:33 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:33.403136 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:33.403398 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:33.403857 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:33.404061 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:33.404257 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:33.404671 [DEB] httpcore.connection	close.started
2025-08-14 18:57:33.404938 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:33.413610 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:33.414714 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:33.416138 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:33.416075Z', 'level': 'debug'}
2025-08-14 18:57:33.416391 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:33.416356Z', 'level': 'debug'}
2025-08-14 18:57:43.366809 [INF] handle_asr_logic	{'event': 'Nhận lệnh: anh đi đi về về hàng ngày nhưng mà bé ít ít thời gian cho gia đình à ừ chuyện đấy bình thường', 'timestamp': '2025-08-14T11:57:43.366712Z', 'level': 'info'}
2025-08-14 18:57:43.375939 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:43.377295 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f20a0>
2025-08-14 18:57:43.377669 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:43.378119 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:43.378244 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:43.378436 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:43.378549 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:43.380254 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:43 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:43.380721 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:43.380971 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:43.381397 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:43.381617 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:43.381811 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:43.382205 [DEB] httpcore.connection	close.started
2025-08-14 18:57:43.382464 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:43.391402 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:43.392492 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:43.393910 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:43.393835Z', 'level': 'debug'}
2025-08-14 18:57:43.394166 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:43.394130Z', 'level': 'debug'}
2025-08-14 18:57:45.268774 [INF] handle_asr_logic	{'event': 'Nhận lệnh: ừm', 'timestamp': '2025-08-14T11:57:45.268676Z', 'level': 'info'}
2025-08-14 18:57:45.278016 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:45.283092 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f23d0>
2025-08-14 18:57:45.284713 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 18:57:45.285358 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 18:57:45.285528 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 18:57:45.285748 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 18:57:45.285869 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 18:57:45.287614 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 11:57:45 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 18:57:45.288126 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 18:57:45.288381 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 18:57:45.288841 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 18:57:45.289054 [DEB] httpcore.http11	response_closed.started
2025-08-14 18:57:45.289251 [DEB] httpcore.http11	response_closed.complete
2025-08-14 18:57:45.289762 [DEB] httpcore.connection	close.started
2025-08-14 18:57:45.290058 [DEB] httpcore.connection	close.complete
2025-08-14 18:57:45.298966 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 18:57:45.300006 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 18:57:45.301040 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T11:57:45.300982Z', 'level': 'debug'}
2025-08-14 18:57:45.301587 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T11:57:45.301548Z', 'level': 'debug'}
2025-08-14 19:09:15.055727 [INF] handle_asr_logic	{'event': 'Nhận lệnh: đang rảnh ở trên công ty đang phải chờ mấy cái trạm sạc sửa gỗ em cái ấy được không kết em test đi chứ không về muộn lại cốt kia mệt lắm', 'timestamp': '2025-08-14T12:09:15.055630Z', 'level': 'info'}
2025-08-14 19:09:15.065232 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:15.066805 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff928458e0>
2025-08-14 19:09:15.067215 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:15.067909 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:15.068053 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:15.068408 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:15.068566 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:15.070203 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:14 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:15.070738 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:15.071002 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:15.071498 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:15.071709 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:15.071907 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:15.072327 [DEB] httpcore.connection	close.started
2025-08-14 19:09:15.072599 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:15.082458 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:15.083556 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:15.084951 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:15.084892Z', 'level': 'debug'}
2025-08-14 19:09:15.085177 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:15.085145Z', 'level': 'debug'}
2025-08-14 19:09:18.893951 [INF] handle_asr_logic	{'event': 'Nhận lệnh: có cái eb socet nên', 'timestamp': '2025-08-14T12:09:18.893855Z', 'level': 'info'}
2025-08-14 19:09:18.903252 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:18.904712 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ef6d0>
2025-08-14 19:09:18.905116 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:18.905629 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:18.905770 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:18.905972 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:18.906086 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:18.907994 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:18 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:18.908520 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:18.908777 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:18.909292 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:18.909520 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:18.909726 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:18.910156 [DEB] httpcore.connection	close.started
2025-08-14 19:09:18.910435 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:18.919254 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:18.920398 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:18.921676 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:18.921601Z', 'level': 'debug'}
2025-08-14 19:09:18.922226 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:18.922185Z', 'level': 'debug'}
2025-08-14 19:09:27.265287 [INF] handle_asr_logic	{'event': 'Nhận lệnh: à bình thường thì mình mở htps rồi ra cho cho người con cẩm tám tư ở cái trang mà em đúng không', 'timestamp': '2025-08-14T12:09:27.265181Z', 'level': 'info'}
2025-08-14 19:09:27.274635 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:27.276082 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f3af0>
2025-08-14 19:09:27.276473 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:27.276940 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:27.277101 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:27.277301 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:27.277418 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:27.279247 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:26 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:27.279754 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:27.280011 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:27.280462 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:27.280663 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:27.280856 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:27.281274 [DEB] httpcore.connection	close.started
2025-08-14 19:09:27.281551 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:27.290651 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:27.291688 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:27.293046 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:27.292979Z', 'level': 'debug'}
2025-08-14 19:09:27.293260 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:27.293223Z', 'level': 'debug'}
2025-08-14 19:09:31.101378 [INF] handle_asr_logic	{'event': 'Nhận lệnh: cái gress debuck ấy', 'timestamp': '2025-08-14T12:09:31.101281Z', 'level': 'info'}
2025-08-14 19:09:31.110966 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:31.117882 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f89a0>
2025-08-14 19:09:31.120446 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:31.121040 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:31.121188 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:31.121417 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:31.121571 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:31.123491 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:30 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:31.124067 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:31.124346 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:31.124876 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:31.125090 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:31.125294 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:31.125786 [DEB] httpcore.connection	close.started
2025-08-14 19:09:31.126075 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:31.135925 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:31.137039 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:31.138254 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:31.138190Z', 'level': 'debug'}
2025-08-14 19:09:31.138813 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:31.138770Z', 'level': 'debug'}
2025-08-14 19:09:44.555157 [INF] handle_asr_logic	{'event': 'Nhận lệnh: em chắt riêng đấy cái g đấy em đi bớt chưa nhìn đúng không thế bây giờ cần một muốn muốn mở thêm cho cái websit cape ra nữa', 'timestamp': '2025-08-14T12:09:44.555064Z', 'level': 'info'}
2025-08-14 19:09:44.564507 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:44.565952 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff92845eb0>
2025-08-14 19:09:44.566326 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:44.566773 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:44.566899 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:44.567093 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:44.567204 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:44.568906 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:43 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:44.569396 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:44.569685 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:44.570130 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:44.570331 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:44.570525 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:44.570929 [DEB] httpcore.connection	close.started
2025-08-14 19:09:44.571196 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:44.580098 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:44.581239 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:44.582803 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:44.582733Z', 'level': 'debug'}
2025-08-14 19:09:44.583075 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:44.583038Z', 'level': 'debug'}
2025-08-14 19:09:51.541626 [INF] handle_asr_logic	{'event': 'Nhận lệnh: sẽ có mình nhỉ', 'timestamp': '2025-08-14T12:09:51.541527Z', 'level': 'info'}
2025-08-14 19:09:51.551283 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:51.552730 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ef310>
2025-08-14 19:09:51.553117 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:51.553599 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:51.553745 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:51.553946 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:51.554062 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:51.555864 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:50 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:51.556360 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:51.556683 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:51.557157 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:51.557397 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:51.557653 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:51.558074 [DEB] httpcore.connection	close.started
2025-08-14 19:09:51.558340 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:51.567362 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:51.568527 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:51.569933 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:51.569872Z', 'level': 'debug'}
2025-08-14 19:09:51.570202 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:51.570162Z', 'level': 'debug'}
2025-08-14 19:09:56.445664 [INF] handle_asr_logic	{'event': 'Nhận lệnh: ông cần được lên sĩ', 'timestamp': '2025-08-14T12:09:56.445567Z', 'level': 'info'}
2025-08-14 19:09:56.454852 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:56.456225 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f2e80>
2025-08-14 19:09:56.456592 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:09:56.457047 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:09:56.457178 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:09:56.457372 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:09:56.457499 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:09:56.459317 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:09:55 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:09:56.459781 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:09:56.460027 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:09:56.460461 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:09:56.460665 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:09:56.460861 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:09:56.461270 [DEB] httpcore.connection	close.started
2025-08-14 19:09:56.461572 [DEB] httpcore.connection	close.complete
2025-08-14 19:09:56.471217 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:09:56.472360 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:09:56.473900 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:09:56.473837Z', 'level': 'debug'}
2025-08-14 19:09:56.474161 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:09:56.474123Z', 'level': 'debug'}
2025-08-14 19:10:09.891075 [INF] handle_asr_logic	{'event': 'Nhận lệnh: congricip ý cái cổng cái con mà giao diện ấy em đang làm em đang cốt cái giải pháp greensip đấu giá mattersulace', 'timestamp': '2025-08-14T12:10:09.890977Z', 'level': 'info'}
2025-08-14 19:10:09.900229 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:09.901672 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f1b20>
2025-08-14 19:10:09.902050 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:10:09.902531 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:10:09.902665 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:10:09.902888 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:10:09.903002 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:10:09.904714 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:10:09 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:10:09.905181 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:10:09.905444 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:10:09.905883 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:10:09.906084 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:10:09.906274 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:10:09.906678 [DEB] httpcore.connection	close.started
2025-08-14 19:10:09.906946 [DEB] httpcore.connection	close.complete
2025-08-14 19:10:09.922313 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:09.925678 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:10:09.927298 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:10:09.927228Z', 'level': 'debug'}
2025-08-14 19:10:09.927502 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:10:09.927472Z', 'level': 'debug'}
2025-08-14 19:10:21.035259 [INF] handle_asr_logic	{'event': 'Nhận lệnh: vâng à em em chat ở trên cái đường này đấy em muốn mở cái cổng tám không tám tư', 'timestamp': '2025-08-14T12:10:21.035162Z', 'level': 'info'}
2025-08-14 19:10:21.044476 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:21.046102 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9288e040>
2025-08-14 19:10:21.046497 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:10:21.046960 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:10:21.047088 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:10:21.047283 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:10:21.047393 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:10:21.049098 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:10:20 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:10:21.049574 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:10:21.049820 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:10:21.050240 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:10:21.050438 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:10:21.050626 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:10:21.051025 [DEB] httpcore.connection	close.started
2025-08-14 19:10:21.051292 [DEB] httpcore.connection	close.complete
2025-08-14 19:10:21.060043 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:21.061156 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:10:21.062606 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:10:21.062538Z', 'level': 'debug'}
2025-08-14 19:10:21.062850 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:10:21.062819Z', 'level': 'debug'}
2025-08-14 19:10:27.185416 [INF] handle_asr_logic	{'event': 'Nhận lệnh: không có cái con một linh tư ngày xưa cấp cho em ấy', 'timestamp': '2025-08-14T12:10:27.185320Z', 'level': 'info'}
2025-08-14 19:10:27.194668 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:27.196140 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927efbe0>
2025-08-14 19:10:27.196523 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:10:27.196991 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:10:27.197125 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:10:27.197325 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:10:27.197449 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:10:27.199190 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:10:26 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:10:27.199668 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:10:27.199921 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:10:27.200354 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:10:27.200556 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:10:27.200748 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:10:27.201160 [DEB] httpcore.connection	close.started
2025-08-14 19:10:27.201450 [DEB] httpcore.connection	close.complete
2025-08-14 19:10:27.210275 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:27.211390 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:10:27.212863 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:10:27.212797Z', 'level': 'debug'}
2025-08-14 19:10:27.213115 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:10:27.213080Z', 'level': 'debug'}
2025-08-14 19:10:32.174142 [INF] handle_asr_logic	{'event': 'Nhận lệnh: cái con mà roler ấy thì con roller ấy', 'timestamp': '2025-08-14T12:10:32.174042Z', 'level': 'info'}
2025-08-14 19:10:32.183319 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:32.184748 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ecf70>
2025-08-14 19:10:32.185120 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:10:32.185598 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:10:32.185737 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:10:32.185944 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:10:32.186058 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:10:32.187754 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:10:31 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:10:32.188207 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:10:32.188447 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:10:32.188880 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:10:32.189080 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:10:32.189288 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:10:32.189719 [DEB] httpcore.connection	close.started
2025-08-14 19:10:32.189996 [DEB] httpcore.connection	close.complete
2025-08-14 19:10:32.198717 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:32.199867 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:10:32.201398 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:10:32.201331Z', 'level': 'debug'}
2025-08-14 19:10:32.201687 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:10:32.201647Z', 'level': 'debug'}
2025-08-14 19:10:41.361245 [INF] handle_asr_logic	{'event': 'Nhận lệnh: vâng cái cây cầu tám không tám tư thì ờ', 'timestamp': '2025-08-14T12:10:41.361151Z', 'level': 'info'}
2025-08-14 19:10:41.370394 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:41.371766 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff928ed2e0>
2025-08-14 19:10:41.372122 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:10:41.372571 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:10:41.372700 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:10:41.372895 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:10:41.373008 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:10:41.374717 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:10:40 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:10:41.375169 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:10:41.375421 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:10:41.375849 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:10:41.376054 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:10:41.376245 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:10:41.376648 [DEB] httpcore.connection	close.started
2025-08-14 19:10:41.376922 [DEB] httpcore.connection	close.complete
2025-08-14 19:10:41.385894 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:10:41.393908 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:10:41.395610 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:10:41.395540Z', 'level': 'debug'}
2025-08-14 19:10:41.395852 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:10:41.395819Z', 'level': 'debug'}
2025-08-14 19:19:46.765618 [INF] handle_asr_logic	{'event': 'Nhận lệnh: em nghe đây', 'timestamp': '2025-08-14T12:19:46.765520Z', 'level': 'info'}
2025-08-14 19:19:46.774821 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:19:46.776201 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff939825e0>
2025-08-14 19:19:46.776560 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:19:46.777011 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:19:46.777136 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:19:46.777329 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:19:46.777451 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:19:46.779155 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:19:45 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:19:46.779607 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:19:46.779850 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:19:46.780273 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:19:46.780467 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:19:46.780655 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:19:46.781049 [DEB] httpcore.connection	close.started
2025-08-14 19:19:46.781315 [DEB] httpcore.connection	close.complete
2025-08-14 19:19:46.789896 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:19:46.790898 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:19:46.792157 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:19:46.792097Z', 'level': 'debug'}
2025-08-14 19:19:46.792407 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:19:46.792371Z', 'level': 'debug'}
2025-08-14 19:20:03.567558 [INF] handle_asr_logic	{'event': 'Nhận lệnh: hoặc là năm một tháng tư ạ à thực ra thì nó hai thằng đấy sẽ cung cấp nếu thế thì để cho em tám phút tám tả', 'timestamp': '2025-08-14T12:20:03.567462Z', 'level': 'info'}
2025-08-14 19:20:03.576992 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:03.578587 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ecb80>
2025-08-14 19:20:03.578995 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:20:03.579503 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:20:03.579646 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:20:03.579864 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:20:03.579983 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:20:03.581783 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:20:02 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:20:03.582311 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:20:03.582584 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:20:03.583062 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:20:03.583274 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:20:03.583481 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:20:03.583916 [DEB] httpcore.connection	close.started
2025-08-14 19:20:03.584197 [DEB] httpcore.connection	close.complete
2025-08-14 19:20:03.592955 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:03.594023 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:20:03.595421 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:20:03.595354Z', 'level': 'debug'}
2025-08-14 19:20:03.595667 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:20:03.595631Z', 'level': 'debug'}
2025-08-14 19:20:11.727676 [INF] handle_asr_logic	{'event': 'Nhận lệnh: nó giống kiểu est pat với es ý', 'timestamp': '2025-08-14T12:20:11.727579Z', 'level': 'info'}
2025-08-14 19:20:11.736842 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:11.738239 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9288e040>
2025-08-14 19:20:11.738611 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:20:11.739065 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:20:11.739197 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:20:11.739397 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:20:11.739511 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:20:11.741303 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:20:10 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:20:11.741830 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:20:11.742084 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:20:11.742537 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:20:11.742733 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:20:11.742922 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:20:11.743325 [DEB] httpcore.connection	close.started
2025-08-14 19:20:11.743587 [DEB] httpcore.connection	close.complete
2025-08-14 19:20:11.752038 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:11.753064 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:20:11.754382 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:20:11.754313Z', 'level': 'debug'}
2025-08-14 19:20:11.754617 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:20:11.754587Z', 'level': 'debug'}
2025-08-14 19:20:19.307996 [INF] handle_asr_logic	{'event': 'Nhận lệnh: anh cứ để cho em tấm hôm tám tám đi tám không tám tám', 'timestamp': '2025-08-14T12:20:19.307890Z', 'level': 'info'}
2025-08-14 19:20:19.317571 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:19.319302 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f2fd0>
2025-08-14 19:20:19.319793 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:20:19.320372 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:20:19.320545 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:20:19.320774 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:20:19.320901 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:20:19.322813 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:20:18 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:20:19.323401 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:20:19.323704 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:20:19.329905 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:20:19.330266 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:20:19.330539 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:20:19.331050 [DEB] httpcore.connection	close.started
2025-08-14 19:20:19.331392 [DEB] httpcore.connection	close.complete
2025-08-14 19:20:19.340747 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:19.342150 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:20:19.343997 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:20:19.343917Z', 'level': 'debug'}
2025-08-14 19:20:19.344433 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:20:19.344383Z', 'level': 'debug'}
2025-08-14 19:20:25.158777 [INF] handle_asr_logic	{'event': 'Nhận lệnh: webrocket nó sẽ http prade lên mà', 'timestamp': '2025-08-14T12:20:25.158675Z', 'level': 'info'}
2025-08-14 19:20:25.168521 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:25.170188 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff92845af0>
2025-08-14 19:20:25.170665 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:20:25.171224 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:20:25.171382 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:20:25.171612 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:20:25.171740 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:20:25.173651 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:20:24 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:20:25.174226 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:20:25.174521 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:20:25.175050 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:20:25.175279 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:20:25.175499 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:20:25.175959 [DEB] httpcore.connection	close.started
2025-08-14 19:20:25.176265 [DEB] httpcore.connection	close.complete
2025-08-14 19:20:25.185336 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:25.186559 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:20:25.188128 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:20:25.188058Z', 'level': 'debug'}
2025-08-14 19:20:25.188396 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:20:25.188357Z', 'level': 'debug'}
2025-08-14 19:20:31.985144 [INF] handle_asr_logic	{'event': 'Nhận lệnh: đúng tấm không tám tám', 'timestamp': '2025-08-14T12:20:31.985052Z', 'level': 'info'}
2025-08-14 19:20:31.994380 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:31.995799 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927eff70>
2025-08-14 19:20:31.996183 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:20:31.996648 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:20:31.996787 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:20:31.996993 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:20:31.997111 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:20:31.998947 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:20:31 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:20:31.999499 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:20:31.999764 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:20:32.000246 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:20:32.000453 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:20:32.000655 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:20:32.001077 [DEB] httpcore.connection	close.started
2025-08-14 19:20:32.001342 [DEB] httpcore.connection	close.complete
2025-08-14 19:20:32.009909 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:32.010908 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:20:32.012249 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:20:32.012184Z', 'level': 'debug'}
2025-08-14 19:20:32.012472 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:20:32.012437Z', 'level': 'debug'}
2025-08-14 19:20:47.344551 [INF] handle_asr_logic	{'event': 'Nhận lệnh: tcb uprate nó sẽ dùng cp upgrade lên anh để cho em cái cổng mà bây giờ anh đang cấu hình ý nhưng mà nó là dành cho', 'timestamp': '2025-08-14T12:20:47.344455Z', 'level': 'info'}
2025-08-14 19:20:47.353743 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:47.355139 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8e50>
2025-08-14 19:20:47.355526 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:20:47.355997 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:20:47.356135 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:20:47.356330 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:20:47.356447 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:20:47.358228 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:20:47 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:20:47.358718 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:20:47.358964 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:20:47.359406 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:20:47.359605 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:20:47.359800 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:20:47.360204 [DEB] httpcore.connection	close.started
2025-08-14 19:20:47.360469 [DEB] httpcore.connection	close.complete
2025-08-14 19:20:47.369048 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:20:47.370095 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:20:47.371408 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:20:47.371345Z', 'level': 'debug'}
2025-08-14 19:20:47.371643 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:20:47.371607Z', 'level': 'debug'}
2025-08-14 19:21:01.155148 [INF] handle_asr_logic	{'event': 'Nhận lệnh: anh thêm giao thức chuyện gì đúng không thêm giao thức thì đúng hơn', 'timestamp': '2025-08-14T12:21:01.155051Z', 'level': 'info'}
2025-08-14 19:21:01.164476 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:01.166044 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8b80>
2025-08-14 19:21:01.166448 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:21:01.166940 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:21:01.167083 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:21:01.167287 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:21:01.167404 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:21:01.169221 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:21:01 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:21:01.169735 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:21:01.169995 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:21:01.170455 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:21:01.170662 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:21:01.170857 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:21:01.171278 [DEB] httpcore.connection	close.started
2025-08-14 19:21:01.171550 [DEB] httpcore.connection	close.complete
2025-08-14 19:21:01.185559 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:01.189034 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:21:01.190727 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:21:01.190659Z', 'level': 'debug'}
2025-08-14 19:21:01.190938 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:21:01.190909Z', 'level': 'debug'}
2025-08-14 19:21:15.072233 [INF] handle_asr_logic	{'event': 'Nhận lệnh: cái của em xem', 'timestamp': '2025-08-14T12:21:15.072131Z', 'level': 'info'}
2025-08-14 19:21:15.081693 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:15.083166 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff928edfa0>
2025-08-14 19:21:15.083571 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:21:15.084043 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:21:15.084177 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:21:15.084380 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:21:15.084496 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:21:15.086349 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:21:14 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:21:15.086865 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:21:15.087123 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:21:15.087584 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:21:15.087789 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:21:15.087985 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:21:15.088402 [DEB] httpcore.connection	close.started
2025-08-14 19:21:15.088679 [DEB] httpcore.connection	close.complete
2025-08-14 19:21:15.097393 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:15.098527 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:21:15.099943 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:21:15.099876Z', 'level': 'debug'}
2025-08-14 19:21:15.100181 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:21:15.100148Z', 'level': 'debug'}
2025-08-14 19:21:37.263038 [INF] handle_asr_logic	{'event': 'Nhận lệnh: ai', 'timestamp': '2025-08-14T12:21:37.262940Z', 'level': 'info'}
2025-08-14 19:21:37.272403 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:37.273908 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8c10>
2025-08-14 19:21:37.274316 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:21:37.274808 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:21:37.274942 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:21:37.275145 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:21:37.275260 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:21:37.277041 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:21:37 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:21:37.277568 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:21:37.277842 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:21:37.278305 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:21:37.278517 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:21:37.278717 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:21:37.279148 [DEB] httpcore.connection	close.started
2025-08-14 19:21:37.279425 [DEB] httpcore.connection	close.complete
2025-08-14 19:21:37.288417 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:37.289628 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:21:37.291181 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:21:37.291113Z', 'level': 'debug'}
2025-08-14 19:21:37.291446 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:21:37.291409Z', 'level': 'debug'}
2025-08-14 19:21:41.113223 [INF] handle_asr_logic	{'event': 'Nhận lệnh: thì mở ra của em để tối về em thử vậy', 'timestamp': '2025-08-14T12:21:41.113126Z', 'level': 'info'}
2025-08-14 19:21:41.122392 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:41.123763 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9293e970>
2025-08-14 19:21:41.124119 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:21:41.124560 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:21:41.124686 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:21:41.124879 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:21:41.124994 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:21:41.126815 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:21:40 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:21:41.127328 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:21:41.127581 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:21:41.128036 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:21:41.128242 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:21:41.128440 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:21:41.128860 [DEB] httpcore.connection	close.started
2025-08-14 19:21:41.129146 [DEB] httpcore.connection	close.complete
2025-08-14 19:21:41.137762 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:41.138918 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:21:41.140328 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:21:41.140261Z', 'level': 'debug'}
2025-08-14 19:21:41.140574 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:21:41.140539Z', 'level': 'debug'}
2025-08-14 19:21:55.400932 [INF] handle_asr_logic	{'event': 'Nhận lệnh: thì anh ngày ngày xưa anh đều có shopee của mình cộng tám mươi hay tổng một bốn ba', 'timestamp': '2025-08-14T12:21:55.400836Z', 'level': 'info'}
2025-08-14 19:21:55.410250 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:55.411751 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff92845ca0>
2025-08-14 19:21:55.412131 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:21:55.412618 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:21:55.412751 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:21:55.412953 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:21:55.413069 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:21:55.414882 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:21:55 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:21:55.415389 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:21:55.415650 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:21:55.416114 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:21:55.416327 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:21:55.416528 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:21:55.416955 [DEB] httpcore.connection	close.started
2025-08-14 19:21:55.417235 [DEB] httpcore.connection	close.complete
2025-08-14 19:21:55.431210 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:21:55.434719 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:21:55.436494 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:21:55.436422Z', 'level': 'debug'}
2025-08-14 19:21:55.436777 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:21:55.436737Z', 'level': 'debug'}
2025-08-14 19:22:15.465563 [INF] handle_asr_logic	{'event': 'Nhận lệnh: em không có chuyên môn cái này lắm cách biết cấu hình thế nào', 'timestamp': '2025-08-14T12:22:15.465465Z', 'level': 'info'}
2025-08-14 19:22:15.474869 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:15.476267 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927eceb0>
2025-08-14 19:22:15.476634 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:22:15.477081 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:22:15.477212 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:22:15.477408 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:22:15.477538 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:22:15.479260 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:22:15 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:22:15.479734 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:22:15.479990 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:22:15.480427 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:22:15.480630 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:22:15.480823 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:22:15.481223 [DEB] httpcore.connection	close.started
2025-08-14 19:22:15.481497 [DEB] httpcore.connection	close.complete
2025-08-14 19:22:15.491002 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:15.492064 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:22:15.493494 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:22:15.493408Z', 'level': 'debug'}
2025-08-14 19:22:15.493762 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:22:15.493725Z', 'level': 'debug'}
2025-08-14 19:22:22.735791 [INF] handle_asr_logic	{'event': 'Nhận lệnh: các bạn mà cái con vô hình của em đúng không có', 'timestamp': '2025-08-14T12:22:22.735693Z', 'level': 'info'}
2025-08-14 19:22:22.745587 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:22.747317 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff928102b0>
2025-08-14 19:22:22.747739 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:22:22.748306 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:22:22.748451 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:22:22.748677 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:22:22.748790 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:22:22.750794 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:22:22 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:22:22.751413 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:22:22.751693 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:22:22.751918 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:22:22.752135 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:22:22.752347 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:22:22.752789 [DEB] httpcore.connection	close.started
2025-08-14 19:22:22.753075 [DEB] httpcore.connection	close.complete
2025-08-14 19:22:22.761917 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:22.763089 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:22:22.764199 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:22:22.764132Z', 'level': 'debug'}
2025-08-14 19:22:22.764656 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:22:22.764624Z', 'level': 'debug'}
2025-08-14 19:22:39.463254 [INF] handle_asr_logic	{'event': 'Nhận lệnh: sửa nó vẫn giữ cái cũ nhưng mà thêm một cái kết nối mới tức là cách làm là như thế này con master nó sẽ không là nó sẽ không bao giờ đấu giá nữa mà nó sẽ bắn về con sled', 'timestamp': '2025-08-14T12:22:39.463163Z', 'level': 'info'}
2025-08-14 19:22:39.472431 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:39.473842 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8880>
2025-08-14 19:22:39.474237 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:22:39.474711 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:22:39.474846 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:22:39.475049 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:22:39.475163 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:22:39.476899 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:22:39 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:22:39.477390 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:22:39.477680 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:22:39.478139 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:22:39.478346 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:22:39.478544 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:22:39.478960 [DEB] httpcore.connection	close.started
2025-08-14 19:22:39.479224 [DEB] httpcore.connection	close.complete
2025-08-14 19:22:39.488190 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:39.489319 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:22:39.490796 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:22:39.490732Z', 'level': 'debug'}
2025-08-14 19:22:39.491095 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:22:39.491057Z', 'level': 'debug'}
2025-08-14 19:22:45.357899 [INF] handle_asr_logic	{'event': 'Nhận lệnh: con slet là một máy clion ở một công ty nào đấy', 'timestamp': '2025-08-14T12:22:45.357803Z', 'level': 'info'}
2025-08-14 19:22:45.367081 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:45.368468 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f9dc0>
2025-08-14 19:22:45.368833 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:22:45.369286 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:22:45.369417 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:22:45.369634 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:22:45.369747 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:22:45.371500 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:22:44 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:22:45.371998 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:22:45.372249 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:22:45.372693 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:22:45.372892 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:22:45.373084 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:22:45.373513 [DEB] httpcore.connection	close.started
2025-08-14 19:22:45.373783 [DEB] httpcore.connection	close.complete
2025-08-14 19:22:45.387342 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:45.390382 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:22:45.391994 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:22:45.391927Z', 'level': 'debug'}
2025-08-14 19:22:45.392207 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:22:45.392173Z', 'level': 'debug'}
2025-08-14 19:22:54.926071 [INF] handle_asr_logic	{'event': 'Nhận lệnh: là em cũng chưa biết đâu', 'timestamp': '2025-08-14T12:22:54.925974Z', 'level': 'info'}
2025-08-14 19:22:54.935647 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:54.937243 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff928ed160>
2025-08-14 19:22:54.937696 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:22:54.938241 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:22:54.938383 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:22:54.938603 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:22:54.938723 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:22:54.940632 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:22:54 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:22:54.941198 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:22:54.941491 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:22:54.941993 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:22:54.942220 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:22:54.942436 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:22:54.942897 [DEB] httpcore.connection	close.started
2025-08-14 19:22:54.943197 [DEB] httpcore.connection	close.complete
2025-08-14 19:22:54.952283 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:22:54.953534 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:22:54.955201 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:22:54.955133Z', 'level': 'debug'}
2025-08-14 19:22:54.955480 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:22:54.955436Z', 'level': 'debug'}
2025-08-14 19:23:13.751545 [INF] handle_asr_logic	{'event': 'Nhận lệnh: như thế em đang thấy chị có thể', 'timestamp': '2025-08-14T12:23:13.751443Z', 'level': 'info'}
2025-08-14 19:23:13.761032 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:13.762631 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8e80>
2025-08-14 19:23:13.763094 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:13.763614 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:13.763753 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:13.763972 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:13.764090 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:13.766031 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:13 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:13.766596 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:13.766864 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:13.767370 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:13.767579 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:13.767781 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:13.768221 [DEB] httpcore.connection	close.started
2025-08-14 19:23:13.768506 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:13.777534 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:13.778687 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:13.780167 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:13.780100Z', 'level': 'debug'}
2025-08-14 19:23:13.780402 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:13.780369Z', 'level': 'debug'}
2025-08-14 19:23:16.389470 [INF] handle_asr_logic	{'event': 'Nhận lệnh: là', 'timestamp': '2025-08-14T12:23:16.389350Z', 'level': 'info'}
2025-08-14 19:23:16.398664 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:16.400054 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8dc0>
2025-08-14 19:23:16.400424 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:16.400877 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:16.401010 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:16.401205 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:16.401320 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:16.403081 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:15 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:16.403568 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:16.403823 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:16.404262 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:16.404464 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:16.404657 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:16.405053 [DEB] httpcore.connection	close.started
2025-08-14 19:23:16.405314 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:16.414171 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:16.415361 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:16.416946 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:16.416878Z', 'level': 'debug'}
2025-08-14 19:23:16.417179 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:16.417149Z', 'level': 'debug'}
2025-08-14 19:23:23.876363 [INF] handle_asr_logic	{'event': 'Nhận lệnh: màu sắc tươi bánh chán', 'timestamp': '2025-08-14T12:23:23.876267Z', 'level': 'info'}
2025-08-14 19:23:23.885691 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:23.887121 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927ef610>
2025-08-14 19:23:23.887486 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:23.887931 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:23.888064 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:23.888260 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:23.888374 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:23.890107 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:23 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:23.890549 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:23.890788 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:23.891196 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:23.891391 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:23.891610 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:23.892018 [DEB] httpcore.connection	close.started
2025-08-14 19:23:23.892278 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:23.906239 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:23.909610 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:23.911312 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:23.911249Z', 'level': 'debug'}
2025-08-14 19:23:23.911572 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:23.911535Z', 'level': 'debug'}
2025-08-14 19:23:29.099352 [INF] handle_asr_logic	{'event': 'Nhận lệnh: và thế làm giống dạng này đi xong tối về test em đi lấy cái trạm xạ', 'timestamp': '2025-08-14T12:23:29.099254Z', 'level': 'info'}
2025-08-14 19:23:29.108669 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:29.110111 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff92809040>
2025-08-14 19:23:29.110508 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:29.110986 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:29.111122 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:29.111326 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:29.111442 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:29.113159 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:28 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:29.113680 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:29.113972 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:29.114431 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:29.114648 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:29.114853 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:29.115270 [DEB] httpcore.connection	close.started
2025-08-14 19:23:29.115540 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:29.124821 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:29.125964 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:29.127513 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:29.127448Z', 'level': 'debug'}
2025-08-14 19:23:29.127789 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:29.127749Z', 'level': 'debug'}
2025-08-14 19:23:35.343410 [INF] handle_asr_logic	{'event': 'Nhận lệnh: c tám đúng không', 'timestamp': '2025-08-14T12:23:35.343317Z', 'level': 'info'}
2025-08-14 19:23:35.352603 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:35.354002 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9293ed60>
2025-08-14 19:23:35.354385 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:35.354843 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:35.354975 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:35.355172 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:35.355289 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:35.357008 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:34 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:35.357517 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:35.357780 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:35.358229 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:35.358435 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:35.358635 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:35.359040 [DEB] httpcore.connection	close.started
2025-08-14 19:23:35.359301 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:35.367944 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:35.368995 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:35.370405 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:35.370340Z', 'level': 'debug'}
2025-08-14 19:23:35.370661 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:35.370623Z', 'level': 'debug'}
2025-08-14 19:23:41.399780 [INF] handle_asr_logic	{'event': 'Nhận lệnh: có gì phích sau vậy vâng', 'timestamp': '2025-08-14T12:23:41.399682Z', 'level': 'info'}
2025-08-14 19:23:41.408966 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:41.410360 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f8f40>
2025-08-14 19:23:41.410748 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:41.411211 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:41.411376 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:41.411583 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:41.411701 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:41.413396 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:40 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:41.413892 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:41.414154 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:41.414604 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:41.414811 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:41.415005 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:41.415411 [DEB] httpcore.connection	close.started
2025-08-14 19:23:41.415670 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:41.424318 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:41.425349 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:41.426741 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:41.426677Z', 'level': 'debug'}
2025-08-14 19:23:41.426983 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:41.426948Z', 'level': 'debug'}
2025-08-14 19:23:50.606521 [INF] handle_asr_logic	{'event': 'Nhận lệnh: ạ vâng ạ tội gì mình thêm một ommen khác càng tốt', 'timestamp': '2025-08-14T12:23:50.606423Z', 'level': 'info'}
2025-08-14 19:23:50.615700 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:50.617100 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff927f9970>
2025-08-14 19:23:50.617492 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:50.617956 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:50.618086 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:50.618283 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:50.618397 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:50.620128 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:49 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:50.620610 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:50.620865 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:50.621296 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:50.621520 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:50.621721 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:50.622128 [DEB] httpcore.connection	close.started
2025-08-14 19:23:50.622404 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:50.637972 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:50.641603 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:50.643199 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:50.643134Z', 'level': 'debug'}
2025-08-14 19:23:50.643408 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:50.643377Z', 'level': 'debug'}
2025-08-14 19:23:53.990467 [INF] handle_asr_logic	{'event': 'Nhận lệnh: vâng này vâng em chào', 'timestamp': '2025-08-14T12:23:53.990368Z', 'level': 'info'}
2025-08-14 19:23:53.999628 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8005 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:54.001026 [DEB] httpcore.connection	connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0xffff9293e280>
2025-08-14 19:23:54.001390 [DEB] httpcore.http11	send_request_headers.started request=<Request [b'GET']>
2025-08-14 19:23:54.001858 [DEB] httpcore.http11	send_request_headers.complete
2025-08-14 19:23:54.001989 [DEB] httpcore.http11	send_request_body.started request=<Request [b'GET']>
2025-08-14 19:23:54.002183 [DEB] httpcore.http11	send_request_body.complete
2025-08-14 19:23:54.002297 [DEB] httpcore.http11	receive_response_headers.started request=<Request [b'GET']>
2025-08-14 19:23:54.003969 [DEB] httpcore.http11	receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'date', b'Thu, 14 Aug 2025 12:23:53 GMT'), (b'server', b'uvicorn'), (b'content-length', b'1258'), (b'content-type', b'application/json')])
2025-08-14 19:23:54.004441 [INF] httpx	HTTP Request: GET http://0.0.0.0:8005/ "HTTP/1.1 200 OK"
2025-08-14 19:23:54.004689 [DEB] httpcore.http11	receive_response_body.started request=<Request [b'GET']>
2025-08-14 19:23:54.005117 [DEB] httpcore.http11	receive_response_body.complete
2025-08-14 19:23:54.005312 [DEB] httpcore.http11	response_closed.started
2025-08-14 19:23:54.005527 [DEB] httpcore.http11	response_closed.complete
2025-08-14 19:23:54.005933 [DEB] httpcore.connection	close.started
2025-08-14 19:23:54.006190 [DEB] httpcore.connection	close.complete
2025-08-14 19:23:54.015150 [DEB] httpcore.connection	connect_tcp.started host='0.0.0.0' port=8001 local_address=None timeout=5 socket_options=None
2025-08-14 19:23:54.016167 [DEB] httpcore.connection	connect_tcp.failed exception=ConnectError(OSError('All connection attempts failed'))
2025-08-14 19:23:54.017527 [DEB] handle_asr_logic	{'event': "Intent result: {'error': 'All connection attempts failed'}", 'timestamp': '2025-08-14T12:23:54.017466Z', 'level': 'debug'}
2025-08-14 19:23:54.017783 [DEB] handle_asr_logic	{'event': 'Không tìm thấy intent phù hợp.', 'timestamp': '2025-08-14T12:23:54.017745Z', 'level': 'debug'}
