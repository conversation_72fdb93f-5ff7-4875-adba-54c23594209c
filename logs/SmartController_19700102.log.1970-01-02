1970-01-02 14:39:02.013624 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ <PERSON><PERSON><PERSON> hình không hợp lệ: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt', 'timestamp': '1970-01-02T06:39:02.012302Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
1970-01-02 14:39:02.018849 [ERR] aws_iot_manager	{'event': '[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT', 'timestamp': '1970-01-02T06:39:02.015942Z', 'level': 'error', 'exception': 'Traceback (most recent call last):\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init\n    cls._client = get_iot_client()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client\n    validate_aws_config()\n  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config\n    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")\nFileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt'}
Traceback (most recent call last):
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/aws_iot_manager.py", line 13, in init
    cls._client = get_iot_client()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 20, in get_iot_client
    validate_aws_config()
  File "/home/<USER>/MainRobot/SmartController/services/aws_iot/mqtt_client.py", line 16, in validate_aws_config
    raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")
FileNotFoundError: [AWS IoT] ❌ File không tồn tại: root_ca → certs/root-CA.crt
1970-01-02 14:39:02.020591 [INF] main	{'event': '✅ Đã khởi tạo component thành công', 'timestamp': '1970-01-02T06:39:02.020482Z', 'level': 'info'}
1970-01-02 14:39:02.037852 [DEB] asyncio	Using selector: EpollSelector
1970-01-02 14:39:02.852374 [INF] mdns_register	{'event': "mDNS: Đăng ký dịch vụ tên 'SmartController._robot._tcp.local.' tại **********:8000 với hostname 'controller.local'", 'timestamp': '1970-01-02T06:39:02.852224Z', 'level': 'info'}
1970-01-02 14:39:06.197577 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '1970-01-02T06:39:06.197465Z', 'level': 'info'}
1970-01-02 14:39:06.203836 [INF] websocket	{'event': 'WebSocket kết nối: device_app', 'timestamp': '1970-01-02T06:39:06.203750Z', 'level': 'info'}
