# app/hardware/camera.py
import pyrealsense2 as rs
from utils.logger import get_logger
from collections import defaultdict

logger = get_logger("camera")

def is_camera_available():
    try:
        ctx = rs.context()
        devices = ctx.query_devices()
        return len(devices) > 0
    except Exception as e:
        logger.error("❌ Không thể kiểm tra camera RealSense", error=str(e))
        return False

def init_camera():
    try:
        ctx = rs.context()
        devices = ctx.query_devices()

        if len(devices) == 0:
            raise RuntimeError("🚫 Không tìm thấy camera RealSense nào")

        logger.info(f"✅ Phát hiện {len(devices)} camera RealSense đang kết nối")

        for i, dev in enumerate(devices):
            logger.info(f"📷 Thiết bị #{i+1} - {dev.get_info(rs.camera_info.name)}", extra={
                "serial_number": dev.get_info(rs.camera_info.serial_number),
                "firmware_version": dev.get_info(rs.camera_info.firmware_version),
                "usb_type": dev.get_info(rs.camera_info.usb_type_descriptor),
                "product_line": dev.get_info(rs.camera_info.product_line)
            })

            sensors = dev.query_sensors()
            for sensor in sensors:
                sensor_name = sensor.get_info(rs.camera_info.name)

                # Gom theo format → resolution → fps list
                format_group = defaultdict(lambda: defaultdict(list))

                for profile in sensor.get_stream_profiles():
                    try:
                        video_profile = profile.as_video_stream_profile()
                        format_ = str(profile.format()).lower()  # ví dụ: format.y8
                        resolution = f"{video_profile.width()}x{video_profile.height()}"
                        fps = video_profile.fps()

                        format_group[format_][resolution].append(fps)

                    except Exception:
                        continue  # bỏ qua stream không phải video

                # Chuyển sang dạng list để log
                grouped = []
                for fmt, res_dict in format_group.items():
                    resolutions = []
                    for res, fps_list in res_dict.items():
                        resolutions.append({
                            "resolution": res,
                            "fps": sorted(list(set(fps_list)))
                        })
                    grouped.append({
                        "format": fmt,
                        "resolutions": resolutions
                    })

                sensor_info = {
                    "sensor": sensor_name,
                    "groups": grouped
                }

                logger.info("🔹 Sensor & Streams", extra=sensor_info)

    except Exception as e:
        logger.exception("❌ Lỗi khi khởi tạo camera RealSense", error=str(e))
        raise
