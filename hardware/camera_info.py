# app/hardware/camera_info.py
import pyrealsense2 as rs
from collections import defaultdict

def get_camera_info():
    ctx = rs.context()
    devices = ctx.query_devices()
    if len(devices) == 0:
        return {"error": "No RealSense device found"}

    device = devices[0]
    info = {
        "name": device.get_info(rs.camera_info.name),
        "serial": device.get_info(rs.camera_info.serial_number),
        "firmware": device.get_info(rs.camera_info.firmware_version),
        "streams": defaultdict(lambda: defaultdict(list))
    }

    for sensor in device.query_sensors():
        for profile in sensor.get_stream_profiles():
            try:
                video_profile = profile.as_video_stream_profile()
                fmt = str(profile.format())
                res = f"{video_profile.width()}x{video_profile.height()}"
                fps = video_profile.fps()
                info["streams"][fmt][res].append(fps)
            except:
                continue  # skip non-video streams

    # <PERSON>yển defaultdict về dict để JSON hóa
    info["streams"] = {
        fmt: dict(res_map) for fmt, res_map in info["streams"].items()
    }

    return info
