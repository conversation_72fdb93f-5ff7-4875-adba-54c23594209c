# /*
# * Copyright 2010-2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
# *
# * Licensed under the Apache License, Version 2.0 (the "License").
# * You may not use this file except in compliance with the License.
# * A copy of the License is located at
# *
# *  http://aws.amazon.com/apache2.0
# *
# * or in the "license" file accompanying this file. This file is distributed
# * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
# * express or implied. See the License for the specific language governing
# * permissions and limitations under the License.
# */

DEFAULT_CONNECT_DISCONNECT_TIMEOUT_SEC = 30
DEFAULT_OPERATION_TIMEOUT_SEC = 5
DEFAULT_DRAINING_INTERNAL_SEC = 0.5
METRICS_PREFIX = "?SDK=Python&Version="
ALPN_PROTCOLS = "x-amzn-mqtt-ca"