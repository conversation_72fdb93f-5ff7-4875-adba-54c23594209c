import logging
import os
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
import structlog

# 📁 Gốc thư mục logs
BASE_LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
os.makedirs(BASE_LOG_DIR, exist_ok=True)

# 📛 Tên project
PROJECT_NAME = "SmartController"

# 📄 Format giống Serilog
LOG_FORMAT = "%(asctime)s [%(levelname).3s] %(name)s\t%(message)s"

# 🧠 Hỗ trợ %f (microseconds) trong thời gian log
class CustomFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        ct = datetime.fromtimestamp(record.created)
        if datefmt:
            return ct.strftime(datefmt)
        else:
            return ct.isoformat()

def create_file_handler():
    today_str = datetime.now().strftime("%Y%m%d")
    log_file_name = f"{PROJECT_NAME}_{today_str}.log"
    log_file_path = os.path.join(BASE_LOG_DIR, log_file_name)

    handler = TimedRotatingFileHandler(
        filename=log_file_path,
        when="midnight",
        interval=1,
        backupCount=7,
        encoding="utf-8"
    )
    handler.setLevel(logging.DEBUG)
    formatter = CustomFormatter(fmt=LOG_FORMAT, datefmt="%Y-%m-%d %H:%M:%S.%f")
    handler.setFormatter(formatter)
    return handler

def create_console_handler():
    handler = logging.StreamHandler()
    handler.setLevel(logging.DEBUG)
    formatter = CustomFormatter(fmt=LOG_FORMAT, datefmt="%Y-%m-%d %H:%M:%S.%f")
    handler.setFormatter(formatter)
    return handler

def setup_structlog():
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 📦 Ghi log ra file + ra console
    root_logger.addHandler(create_file_handler())
    root_logger.addHandler(create_console_handler())

    structlog.configure(
        processors=[
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

def get_logger(name: str = None):
    return structlog.get_logger(name or __name__)
